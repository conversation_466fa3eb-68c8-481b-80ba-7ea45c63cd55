import SwiftUI

struct RadioStation: Identifiable {
    let id = UUID()
    let name: String
    let url: String
}

struct RadioView: View {
    @State private var timerState: TimerState = .initial
    @State private var selectedStation: RadioStation?
    
    let stations = [
        RadioStation(name: "怀集音乐之声", url: "http://lhttp.qingting.fm/live/4804/64k.mp3"),
        RadioStation(name: "广东音乐之声", url: "https://lhttp-hw.qingting.fm/live/1260/64k.mp3"),
        RadioStation(name: "星岛中文粤语", url: "https://nap.casthost.net/proxy/florenc1/stream")
    ]

    var body: some View {
        VStack {
            Spacer()
            
            VStack(spacing: 20) {
                if let station = selectedStation {
                    Text(station.name)
                        .font(.headline)
                        .foregroundColor(Color(hex: "8A2BE2"))
                    
                    Text(timerState == .running ? "正在播放" : "停止")
                        .foregroundColor(Color(hex: "8A2BE2"))
                        .font(.subheadline)
                }
                
                ForEach(stations) { station in
                    Button(action: {
                        if timerState == .running {
                            if selectedStation?.id == station.id {
                                RadioManager.shared.stopRadio()
                                timerState = .initial
                                selectedStation = nil
                            } else {
                                RadioManager.shared.stopRadio()
                                RadioManager.shared.playRadio(url: station.url, stationName: station.name)
                                selectedStation = station
                            }
                        } else {
                            RadioManager.shared.playRadio(url: station.url, stationName: station.name)
                            timerState = .running
                            selectedStation = station
                        }
                    }) {
                        HStack {
                            Text(station.name)
                                .foregroundColor(Color(hex: "8A2BE2"))
                            
                            if selectedStation?.id == station.id {
                                Image(systemName: timerState == .running ? "pause.circle.fill" : "play.circle.fill")
                                    .resizable()
                                    .frame(width: 30, height: 30)
                                    .foregroundColor(Color(hex: "9370DB"))
                            } else {
                                Image(systemName: "play.circle.fill")
                                    .resizable()
                                    .frame(width: 30, height: 30)
                                    .foregroundColor(Color(hex: "E6E6FA"))
                            }
                        }
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color(hex: selectedStation?.id == station.id ? "9370DB" : "E6E6FA"), lineWidth: 2)
                        )
                    }
                }

                if timerState == .running {
                    Spacer()
                        .frame(height: 30)  // 增加上方间距
                    
                    Button(action: {
                        RadioManager.shared.stopRadio()
                        timerState = .initial
                        selectedStation = nil
                    }) {
                        HStack {
                            Image(systemName: "stop.circle.fill")
                                .resizable()
                                .frame(width: 24, height: 24)
                            Text("一键停止")
                                .font(.headline)
                        }
                        .foregroundColor(.red)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.red, lineWidth: 2)
                        )
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.horizontal)
            
            Spacer()
        }
        .padding(.vertical)
    }
}

#Preview {
    RadioView()
}
