import SwiftUI
import AVFoundation

// 主要定时器状态枚举
enum TimerState {
    case initial
    case running
    case paused
}

// 主视图
struct ContentView: View {
    @State private var selectedTab = 0
    @State private var attemptedTab = 0
    @State private var showingAlert = false
    @State private var tomatoTimerRunning = false
    @State private var tabataTimerRunning = false

    var body: some View {
        TabView(selection: $selectedTab) {
            TomatoTimerView(isRunning: $tomatoTimerRunning)
                .tabItem {
                    Image(systemName: "timer")
                    Text("番茄钟")
                }
                .tag(0)

            RadioView()
                .tabItem {
                    Image(systemName: "radio")
                    Text("收音机")
                }
                .tag(1)

            TabataTimerView(isRunning: $tabataTimerRunning)
                .tabItem {
                    Image(systemName: "figure.run")
                    Text("Tabata")
                }
                .tag(2)
        }
        .accentColor(Color(hex: "9370DB"))
        .interactiveDismissDisabled()
        .onChange(of: selectedTab) { oldValue, newValue in
            if newValue == 2 && tomatoTimerRunning {
                attemptedTab = newValue
                showingAlert = true
                selectedTab = oldValue
            } else if newValue == 0 && tabataTimerRunning {
                attemptedTab = newValue
                showingAlert = true
                selectedTab = oldValue
            }
        }
        .alert(isPresented: $showingAlert) {
            Alert(
                title: Text("正在进行中"),
                message: Text(tomatoTimerRunning ? "番茄钟正在运行中，是否停止并切换？" : "Tabata正在运行中，是否停止并切换？"),
                primaryButton: .destructive(Text("停止并切换")) {
                    tomatoTimerRunning = false
                    tabataTimerRunning = false
                    selectedTab = attemptedTab
                },
                secondaryButton: .cancel(Text("取消"))
            )
        }
    }
}

// 番茄钟视图
struct TomatoTimerView: View {
    @State private var timeRemaining = 25 * 60
    @State private var timerState: TimerState = .initial
    @State private var workTime = 25 * 60
    @State private var breakTime = 10 * 60
    @State private var timer: Timer?
    @Binding var isRunning: Bool
    @State private var isWorking = true

    var body: some View {
        VStack {
            CircularProgressView(progress: 1 - Double(timeRemaining) / Double(isWorking ? workTime : breakTime),
                               timeRemaining: timeRemaining,
                               color: Color(hex: "9370DB"))
                .frame(width: 280, height: 280)
                .padding()
            
            if timerState != .initial {
                VStack(spacing: 20) {
                    Text(isWorking ? "工作进行中，请关注当下哦" : "该休息了，站起来喝杯水吧～")
                        .font(.headline)
                        .foregroundColor(Color(hex: "9370DB"))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    HStack(spacing: 20) {
                        if timerState == .running {
                            Button(action: pauseTimer) {
                                Image(systemName: "pause.circle.fill")
                                    .resizable()
                                    .frame(width: 60, height: 60)
                                    .foregroundColor(Color(hex: "9370DB"))
                            }
                        } else if timerState == .paused {
                            Button(action: startTimer) {
                                Image(systemName: "play.circle.fill")
                                    .resizable()
                                    .frame(width: 60, height: 60)
                                    .foregroundColor(Color(hex: "9370DB"))
                            }
                        }
                        
                        Button(action: resetTimer) {
                            Image(systemName: "arrow.counterclockwise.circle.fill")
                                .resizable()
                                .frame(width: 60, height: 60)
                                .foregroundColor(Color(hex: "E6E6FA"))
                        }
                    }
                }
                .padding(.vertical)
            }
            
            if timerState == .initial {
                HStack(spacing: 50) {
                    // Work time adjustment
                    VStack {
                        Text("工作时间")
                            .font(.subheadline)
                            .foregroundColor(Color(hex: "9370DB"))
                        HStack(spacing: 20) {
                            Button(action: {
                                if workTime > 5 * 60 {
                                    workTime -= 5 * 60
                                    if isWorking {
                                        timeRemaining = workTime
                                    }
                                }
                            }) {
                                Image(systemName: "minus.circle.fill")
                                    .resizable()
                                    .frame(width: 30, height: 30)
                                    .foregroundColor(Color(hex: "9370DB"))
                            }
                            
                            Text("\(workTime / 60)")
                                .font(.headline)
                                .foregroundColor(Color(hex: "9370DB"))
                            
                            Button(action: {
                                if workTime < 60 * 60 {
                                    workTime += 5 * 60
                                    if isWorking {
                                        timeRemaining = workTime
                                    }
                                }
                            }) {
                                Image(systemName: "plus.circle.fill")
                                    .resizable()
                                    .frame(width: 30, height: 30)
                                    .foregroundColor(Color(hex: "9370DB"))
                            }
                        }
                        
                        Button(action: {
                            isWorking = true
                            timeRemaining = workTime
                            startTimer()
                        }) {
                            Image(systemName: "play.circle.fill")
                                .resizable()
                                .frame(width: 30, height: 30)
                                .foregroundColor(Color(hex: "9370DB"))
                        }
                        .padding(.top, 10)
                    }
                    
                    // Break time adjustment
                    VStack {
                        Text("休息时间")
                            .font(.subheadline)
                            .foregroundColor(Color(hex: "9370DB"))
                        HStack(spacing: 20) {
                            Button(action: {
                                if breakTime > 5 * 60 {
                                    breakTime -= 5 * 60
                                    if !isWorking {
                                        timeRemaining = breakTime
                                    }
                                }
                            }) {
                                Image(systemName: "minus.circle.fill")
                                    .resizable()
                                    .frame(width: 30, height: 30)
                                    .foregroundColor(Color(hex: "9370DB"))
                            }
                            
                            Text("\(breakTime / 60)")
                                .font(.headline)
                                .foregroundColor(Color(hex: "9370DB"))
                            
                            Button(action: {
                                if breakTime < 30 * 60 {
                                    breakTime += 5 * 60
                                    if !isWorking {
                                        timeRemaining = breakTime
                                    }
                                }
                            }) {
                                Image(systemName: "plus.circle.fill")
                                    .resizable()
                                    .frame(width: 30, height: 30)
                                    .foregroundColor(Color(hex: "9370DB"))
                            }
                        }
                        
                        Button(action: {
                            isWorking = false
                            timeRemaining = breakTime
                            startTimer()
                        }) {
                            Image(systemName: "play.circle.fill")
                                .resizable()
                                .frame(width: 30, height: 30)
                                .foregroundColor(Color(hex: "9370DB"))
                        }
                        .padding(.top, 10)
                    }
                }
                .padding(.vertical)
            }
        }
        .padding()
        .onChange(of: isRunning) { _, running in
            if !running && timerState == .running {
                pauseTimer()
                resetTimer()
            }
        }
    }
    
    private func startTimer() {
        if timerState == .initial {
            timeRemaining = isWorking ? workTime : breakTime
        }
        timerState = .running
        isRunning = true
        UIApplication.shared.isIdleTimerDisabled = true  // 防止屏幕休眠
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            if timeRemaining > 0 {
                timeRemaining -= 1
            } else {
                timer?.invalidate()
                playCompletionFeedback()
                switchTimer()
            }
        }
    }
    
    private func pauseTimer() {
        timer?.invalidate()
        timerState = .paused
        isRunning = false
        UIApplication.shared.isIdleTimerDisabled = false  // 允许屏幕休眠
    }
    
    private func resetTimer() {
        timer?.invalidate()
        isWorking = true
        timeRemaining = workTime
        timerState = .initial
        isRunning = false
        UIApplication.shared.isIdleTimerDisabled = false  // 允许屏幕休眠
    }
    
    private func switchTimer() {
        isWorking.toggle()
        timeRemaining = isWorking ? workTime : breakTime
        timer?.invalidate()
        timerState = .running
        isRunning = true
        UIApplication.shared.isIdleTimerDisabled = true  // 保持防止屏幕休眠
        
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            if timeRemaining > 0 {
                timeRemaining -= 1
            } else {
                timer?.invalidate()
                playCompletionFeedback()
                switchTimer()
            }
        }
    }
    
    private func playCompletionFeedback() {
        UINotificationFeedbackGenerator().notificationOccurred(.success)
        SoundManager.shared.playSound()
    }
}

// Tabata定时器视图
struct TabataTimerView: View {
    @State private var exerciseTime = 50
    @State private var restTime = 10
    @State private var rounds = 10
    @State private var currentRound = 1
    @State private var isExercising = true
    @State private var timeRemaining = 50
    @State private var timerState: TimerState = .initial
    @State private var timer: Timer?
    @Binding var isRunning: Bool
    
    var body: some View {
        VStack {
            if timerState == .initial {
                TabataSettingsView(
                    exerciseTime: $exerciseTime,
                    restTime: $restTime,
                    rounds: $rounds
                )
                
                Button(action: startTabataTimer) {
                    Image(systemName: "play.circle.fill")
                        .resizable()
                        .frame(width: 60, height: 60)
                        .foregroundColor(Color(hex: "9370DB"))
                }
                .padding(.top)
            } else {
                VStack(spacing: 20) {
                    Text(isExercising ? "运动" : "休息")
                        .font(.title)
                        .foregroundColor(isExercising ? Color(hex: "8A2BE2") : Color(hex: "E6E6FA"))
                    
                    CircularProgressView(
                        progress: 1 - Double(timeRemaining) / Double(isExercising ? exerciseTime : restTime),
                        timeRemaining: timeRemaining,
                        color: isExercising ? Color(hex: "8A2BE2") : Color(hex: "E6E6FA")
                    )
                    .frame(width: 280, height: 280)
                    
                    Text("第 \(currentRound)/\(rounds) 组")
                        .font(.title2)
                        .padding()
                    
                    HStack(spacing: 20) {
                        if timerState == .running {
                            Button(action: pauseTabataTimer) {
                                Image(systemName: "pause.circle.fill")
                                    .resizable()
                                    .frame(width: 60, height: 60)
                                    .foregroundColor(Color(hex: "9370DB"))
                            }
                        } else if timerState == .paused {
                            Button(action: startTabataTimer) {
                                Image(systemName: "play.circle.fill")
                                    .resizable()
                                    .frame(width: 60, height: 60)
                                    .foregroundColor(Color(hex: "9370DB"))
                            }
                        }
                        
                        Button(action: resetTabataTimer) {
                            Image(systemName: "arrow.counterclockwise.circle.fill")
                                .resizable()
                                .frame(width: 60, height: 60)
                                .foregroundColor(Color(hex: "E6E6FA"))
                        }
                    }
                }
            }
        }
        .padding()
        .onChange(of: isRunning) { _, running in
            if !running && timerState == .running {
                pauseTabataTimer()
                resetTabataTimer()
            }
        }
    }
    
    private func startTabataTimer() {
        timerState = .running
        isRunning = true
        UIApplication.shared.isIdleTimerDisabled = true  // 防止屏幕休眠
        if timerState == .initial {
            timeRemaining = exerciseTime
            isExercising = true
            currentRound = 1
        }
        
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            if timeRemaining > 0 {
                if isExercising {
                    SoundManager.shared.playTickSound()
                }
                timeRemaining -= 1
            } else {
                if currentRound < rounds || (currentRound == rounds && isExercising) {
                    if isExercising {
                        playTransitionFeedback()
                        timeRemaining = restTime
                        isExercising = false
                        UIApplication.shared.isIdleTimerDisabled = true  // 保持防止屏幕休眠
                    } else {
                        playTransitionFeedback()
                        timeRemaining = exerciseTime
                        isExercising = true
                        currentRound += 1
                        UIApplication.shared.isIdleTimerDisabled = true  // 保持防止屏幕休眠
                    }
                } else {
                    timer?.invalidate()
                    resetTabataTimer()
                    playCompletionFeedback()
                }
            }
        }
    }
    
    private func pauseTabataTimer() {
        timer?.invalidate()
        timerState = .paused
        isRunning = false
        UIApplication.shared.isIdleTimerDisabled = false  // 允许屏幕休眠
    }
    
    private func resetTabataTimer() {
        timer?.invalidate()
        timerState = .initial
        currentRound = 1
        isExercising = true
        timeRemaining = exerciseTime
        isRunning = false
        UIApplication.shared.isIdleTimerDisabled = false  // 允许屏幕休眠
    }
    
    private func playTransitionFeedback() {
        UINotificationFeedbackGenerator().notificationOccurred(.warning)
        if isExercising {
            SoundManager.shared.playExerciseEndSound()
        } else {
            SoundManager.shared.playSound()
        }
    }
    
    private func playCompletionFeedback() {
        UINotificationFeedbackGenerator().notificationOccurred(.success)
        SoundManager.shared.playTabataCompleteSound()
    }
}

// Tabata设置视图
struct TabataSettingsView: View {
    @Binding var exerciseTime: Int
    @Binding var restTime: Int
    @Binding var rounds: Int
    
    var body: some View {
        VStack(spacing: 30) {
            Text("Tabata 设置")
                .font(.title)
                .foregroundColor(Color(hex: "9370DB"))
                .padding(.bottom, 20)
            
            VStack(alignment: .leading, spacing: 25) {
                VStack(alignment: .leading, spacing: 10) {
                    Text("运动时间")
                        .foregroundColor(Color(hex: "8A2BE2"))
                        .font(.headline)
                    Stepper("\(exerciseTime) 秒", value: $exerciseTime, in: 5...60, step: 5)
                }
                
                VStack(alignment: .leading, spacing: 10) {
                    Text("休息时间")
                        .foregroundColor(Color(hex: "8A2BE2"))
                        .font(.headline)
                    Stepper("\(restTime) 秒", value: $restTime, in: 5...60, step: 5)
                }
                
                VStack(alignment: .leading, spacing: 10) {
                    Text("训练组数")
                        .foregroundColor(Color(hex: "8A2BE2"))
                        .font(.headline)
                    Stepper("\(rounds) 组", value: $rounds, in: 1...20)
                }
            }
            .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
}

// 圆形进度视图
struct CircularProgressView: View {
    let progress: Double
    let timeRemaining: Int
    let color: Color
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(color.opacity(0.2), lineWidth: 15)
            
            Circle()
                .trim(from: 0, to: CGFloat(min(progress, 1.0)))
                .stroke(color, style: StrokeStyle(lineWidth: 15, lineCap: .round))
                .rotationEffect(.degrees(-90))
                .animation(.linear(duration: 1), value: progress)
            
            VStack {
                Text("\(timeRemaining / 60):\(String(format: "%02d", timeRemaining % 60))")
                    .font(.system(size: 50, weight: .bold))
                    .foregroundColor(color)
            }
        }
    }
}

extension Color {
    init(hex: String) {
        let scanner = Scanner(string: hex)
        var rgbValue: UInt64 = 0
        scanner.scanHexInt64(&rgbValue)
        
        let r = Double((rgbValue & 0xFF0000) >> 16) / 255.0
        let g = Double((rgbValue & 0x00FF00) >> 8) / 255.0
        let b = Double(rgbValue & 0x0000FF) / 255.0
        
        self.init(red: r, green: g, blue: b)
    }
}

#Preview {
    ContentView()
}
