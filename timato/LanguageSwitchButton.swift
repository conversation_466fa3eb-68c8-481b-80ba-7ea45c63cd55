//
//  LanguageSwitchButton.swift
//  timato
//
//  Created for Timato App
//

import SwiftUI

struct LanguageSwitchButton: View {
    @ObservedObject private var languageManager = LanguageManager.shared
    @State private var showingLanguageMenu = false
    
    var body: some View {
        Button(action: {
            showingLanguageMenu = true
        }) {
            HStack(spacing: 4) {
                Image(systemName: "globe")
                    .font(.system(size: 16, weight: .medium))
                Text(languageManager.currentLanguage.flag)
                    .font(.system(size: 14))
            }
            .foregroundColor(Color(hex: "9370DB"))
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(hex: "9370DB").opacity(0.1))
            )
        }
        .confirmationDialog(
            NSLocalizedString("language.switch", comment: "Switch language title"),
            isPresented: $showingLanguageMenu,
            titleVisibility: .visible
        ) {
            ForEach(SupportedLanguage.allCases, id: \.self) { language in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        languageManager.setLanguage(language)
                    }
                }) {
                    HStack {
                        Text(language.flag)
                        Text(language.displayName)
                        if language == languageManager.currentLanguage {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
            
            Button(NSLocalizedString("action.cancel", comment: "Cancel"), role: .cancel) { }
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            // 强制刷新视图以应用新的本地化字符串
            DispatchQueue.main.async {
                // 触发视图重新渲染
            }
        }
    }
}

// 简化版语言切换按钮（仅切换，不显示菜单）
struct SimpleLanguageSwitchButton: View {
    @ObservedObject private var languageManager = LanguageManager.shared
    @State private var isAnimating = false
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                isAnimating = true
                languageManager.toggleLanguage()
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                isAnimating = false
            }
        }) {
            HStack(spacing: 6) {
                Image(systemName: "globe")
                    .font(.system(size: 16, weight: .medium))
                    .rotationEffect(.degrees(isAnimating ? 360 : 0))
                
                Text(languageManager.currentLanguage.flag)
                    .font(.system(size: 16))
                    .scaleEffect(isAnimating ? 1.2 : 1.0)
            }
            .foregroundColor(Color(hex: "9370DB"))
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color(hex: "9370DB").opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color(hex: "9370DB").opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            // 强制刷新视图以应用新的本地化字符串
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        LanguageSwitchButton()
        SimpleLanguageSwitchButton()
    }
    .padding()
}
