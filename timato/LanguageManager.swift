//
//  LanguageManager.swift
//  timato
//
//  Created for Timato App
//

import Foundation
import SwiftUI
import ObjectiveC

// 支持的语言枚举
enum SupportedLanguage: String, CaseIterable {
    case english = "en"
    case chinese = "zh-Hans"
    
    var displayName: String {
        switch self {
        case .english:
            return "English"
        case .chinese:
            return "中文"
        }
    }
    
    var flag: String {
        switch self {
        case .english:
            return "🇺🇸"
        case .chinese:
            return "🇨🇳"
        }
    }
}

// 语言管理器类
class LanguageManager: ObservableObject {
    static let shared = LanguageManager()
    
    @Published var currentLanguage: SupportedLanguage {
        didSet {
            saveLanguagePreference()
            updateAppLanguage()
        }
    }
    
    private let userDefaults = UserDefaults.standard
    private let languageKey = "AppLanguage"
    
    private init() {
        // 从UserDefaults读取保存的语言偏好，如果没有则使用系统语言
        if let savedLanguage = userDefaults.string(forKey: languageKey),
           let language = SupportedLanguage(rawValue: savedLanguage) {
            self.currentLanguage = language
        } else {
            // 根据系统语言设置默认语言
            let systemLanguage = Locale.current.language.languageCode?.identifier ?? "en"
            if systemLanguage.hasPrefix("zh") {
                self.currentLanguage = .chinese
            } else {
                self.currentLanguage = .english
            }
        }
        
        updateAppLanguage()
    }
    
    // 切换语言
    func toggleLanguage() {
        switch currentLanguage {
        case .english:
            currentLanguage = .chinese
        case .chinese:
            currentLanguage = .english
        }
    }
    
    // 设置特定语言
    func setLanguage(_ language: SupportedLanguage) {
        currentLanguage = language
    }
    
    // 保存语言偏好到UserDefaults
    private func saveLanguagePreference() {
        userDefaults.set(currentLanguage.rawValue, forKey: languageKey)
    }
    
    // 更新应用语言
    private func updateAppLanguage() {
        // 设置Bundle的语言
        Bundle.setLanguage(currentLanguage.rawValue)
        
        // 发送通知，让UI重新加载
        NotificationCenter.default.post(name: .languageChanged, object: nil)
    }
    
    // 获取本地化字符串
    func localizedString(for key: String, comment: String = "") -> String {
        return NSLocalizedString(key, comment: comment)
    }
}

// 通知名称扩展
extension Notification.Name {
    static let languageChanged = Notification.Name("LanguageChanged")
}

// Bundle扩展，用于动态切换语言
extension Bundle {
    static var bundle: Bundle!

    public static func setLanguage(_ language: String) {
        defer {
            object_setClass(Bundle.main, AnyLanguageBundle.self)
        }

        guard let path = Bundle.main.path(forResource: language, ofType: "lproj"),
              let bundle = Bundle(path: path) else {
            // 如果找不到指定语言包，使用英文作为默认
            guard let path = Bundle.main.path(forResource: "en", ofType: "lproj"),
                  let bundle = Bundle(path: path) else {
                self.bundle = Bundle.main
                return
            }
            self.bundle = bundle
            return
        }

        self.bundle = bundle
    }
}

// 自定义Bundle类，用于动态语言切换
class AnyLanguageBundle: Bundle, @unchecked Sendable {
    override func localizedString(forKey key: String, value: String?, table tableName: String?) -> String {
        guard let bundle = Bundle.bundle else {
            return super.localizedString(forKey: key, value: value, table: tableName)
        }
        return bundle.localizedString(forKey: key, value: value, table: tableName)
    }
}

// SwiftUI环境值扩展
struct LanguageManagerKey: EnvironmentKey {
    static let defaultValue = LanguageManager.shared
}

extension EnvironmentValues {
    var languageManager: LanguageManager {
        get { self[LanguageManagerKey.self] }
        set { self[LanguageManagerKey.self] = newValue }
    }
}
