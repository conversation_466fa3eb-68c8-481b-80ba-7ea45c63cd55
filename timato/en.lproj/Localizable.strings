/* 
  Localizable.strings (English)
  timato
  
  Created for Timato App
*/

// MARK: - Tab Bar Items
"tab.pomodoro" = "Pomodoro";
"tab.radio" = "Radio";
"tab.tabata" = "Tabata";

// MARK: - Common Actions
"action.start" = "Start";
"action.pause" = "Pause";
"action.reset" = "Reset";
"action.stop" = "Stop";
"action.cancel" = "Cancel";
"action.add" = "Add";
"action.delete" = "Delete";
"action.confirm" = "Confirm";

// MARK: - Alert Messages
"alert.timer_running.title" = "Timer Running";
"alert.pomodoro_running.message" = "Pomodoro timer is running. Stop and switch?";
"alert.tabata_running.message" = "Tabata timer is running. Stop and switch?";
"alert.stop_and_switch" = "Stop & Switch";
"alert.delete_station.title" = "Delete Station";
"alert.delete_station.message" = "Are you sure you want to delete station \"%@\"?";

// MARK: - Pomodoro Timer
"pomodoro.work_status" = "Focus time! Stay present and productive";
"pomodoro.break_status" = "Break time! Stand up and hydrate";
"pomodoro.work_time" = "Work Time";
"pomodoro.break_time" = "Break Time";
"pomodoro.minutes_format" = "%d minutes";
"pomodoro.work_completed" = "Work session completed!";
"pomodoro.break_completed" = "Break time is over!";

// MARK: - Radio
"radio.playing" = "Playing";
"radio.stopped" = "Stopped";
"radio.add_station" = "Add Station";
"radio.preset_stations" = "Preset Stations";
"radio.custom_stations" = "Custom Stations";

// MARK: - Add Station
"add_station.title" = "Add Custom Station";
"add_station.name_label" = "Station Name";
"add_station.name_placeholder" = "Enter station name";
"add_station.url_label" = "Stream URL";
"add_station.url_placeholder" = "Enter radio stream URL";
"add_station.url_description" = "Supports HTTP/HTTPS audio streams";
"add_station.example_label" = "Example format:";
"add_station.example_url" = "http://example.com/stream.mp3";
"add_station.validating" = "Validating...";
"add_station.add_button" = "Add Station";
"add_station.cancel_button" = "Cancel";

// MARK: - Add Station Errors
"add_station.error.input_error" = "Input Error";
"add_station.error.input_message" = "Please fill in all station information";
"add_station.error.url_format" = "URL Format Error";
"add_station.error.url_format_message" = "Please enter a valid URL";
"add_station.error.add_failed" = "Add Failed";
"add_station.error.add_failed_message" = "Station name or URL already exists";
"add_station.error.url_validation_failed" = "URL Validation Failed";
"add_station.error.url_validation_message" = "Cannot connect to the station. Please check the URL";

// MARK: - Tabata Timer
"tabata.settings" = "Tabata Settings";
"tabata.exercise_time" = "Exercise Time";
"tabata.rest_time" = "Rest Time";
"tabata.rounds" = "Rounds";
"tabata.seconds_format" = "%d seconds";
"tabata.rounds_format" = "%d rounds";
"tabata.round_progress" = "Round %d/%d";
"tabata.exercise" = "Exercise";
"tabata.rest" = "Rest";
"tabata.get_ready" = "Get Ready";
"tabata.completed" = "Workout Complete!";

// MARK: - Time Formats
"time.minutes" = "minutes";
"time.seconds" = "seconds";
"time.format.mm_ss" = "%02d:%02d";
