import SwiftUI

struct RadioView: View {
    @StateObject private var stationManager = RadioStationManager.shared
    @State private var timerState: TimerState = .initial
    @State private var selectedStation: RadioStation?
    @State private var showingAddStationSheet = false
    @State private var showingDeleteAlert = false
    @State private var stationToDelete: RadioStation?

    var body: some View {
        VStack {
            Spacer()

            VStack(spacing: 20) {
                if let station = selectedStation {
                    Text(station.name)
                        .font(.headline)
                        .foregroundColor(Color(hex: "8A2BE2"))

                    Text(timerState == .running ? "正在播放" : "停止")
                        .foregroundColor(Color(hex: "8A2BE2"))
                        .font(.subheadline)
                }

                // 添加电台按钮
                Button(action: {
                    showingAddStationSheet = true
                }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                            .resizable()
                            .frame(width: 24, height: 24)
                        Text("添加电台")
                            .font(.headline)
                    }
                    .foregroundColor(Color(hex: "9370DB"))
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color(hex: "9370DB"), lineWidth: 2)
                            .background(Color(hex: "9370DB").opacity(0.1))
                    )
                }

                // 预设电台标题
                if !stationManager.presetStationsList.isEmpty {
                    HStack {
                        Text("预设电台")
                            .font(.subheadline)
                            .foregroundColor(Color(hex: "8A2BE2"))
                            .fontWeight(.semibold)
                        Spacer()
                    }
                    .padding(.horizontal)
                }

                // 预设电台列表
                ForEach(stationManager.presetStationsList) { station in
                    stationButton(for: station)
                }

                // 自定义电台标题和列表
                if !stationManager.customStationsList.isEmpty {
                    HStack {
                        Text("自定义电台")
                            .font(.subheadline)
                            .foregroundColor(Color(hex: "8A2BE2"))
                            .fontWeight(.semibold)
                        Spacer()
                    }
                    .padding(.horizontal)
                    .padding(.top, 10)

                    ForEach(stationManager.customStationsList) { station in
                        customStationButton(for: station)
                    }
                }

                if timerState == .running {
                    Spacer()
                        .frame(height: 30)  // 增加上方间距
                    
                    Button(action: {
                        RadioManager.shared.stopRadio()
                        timerState = .initial
                        selectedStation = nil
                    }) {
                        HStack {
                            Image(systemName: "stop.circle.fill")
                                .resizable()
                                .frame(width: 24, height: 24)
                            Text("一键停止")
                                .font(.headline)
                        }
                        .foregroundColor(.red)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.red, lineWidth: 2)
                        )
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.horizontal)
            
            Spacer()
        }
        .padding(.vertical)
        .sheet(isPresented: $showingAddStationSheet) {
            AddStationView(stationManager: stationManager)
        }
        .alert("删除电台", isPresented: $showingDeleteAlert) {
            Button("删除", role: .destructive) {
                if let station = stationToDelete {
                    _ = stationManager.deleteCustomStation(station)
                    // 如果删除的是当前播放的电台，停止播放
                    if selectedStation?.id == station.id {
                        RadioManager.shared.stopRadio()
                        timerState = .initial
                        selectedStation = nil
                    }
                }
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("确定要删除电台 \"\(stationToDelete?.name ?? "")\" 吗？")
        }
    }

    // 普通电台按钮
    private func stationButton(for station: RadioStation) -> some View {
        Button(action: {
            handleStationTap(station)
        }) {
            HStack {
                Text(station.name)
                    .foregroundColor(Color(hex: "8A2BE2"))

                Spacer()

                if selectedStation?.id == station.id {
                    Image(systemName: timerState == .running ? "pause.circle.fill" : "play.circle.fill")
                        .resizable()
                        .frame(width: 30, height: 30)
                        .foregroundColor(Color(hex: "9370DB"))
                } else {
                    Image(systemName: "play.circle.fill")
                        .resizable()
                        .frame(width: 30, height: 30)
                        .foregroundColor(Color(hex: "E6E6FA"))
                }
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color(hex: selectedStation?.id == station.id ? "9370DB" : "E6E6FA"), lineWidth: 2)
            )
        }
    }

    // 自定义电台按钮（带删除功能）
    private func customStationButton(for station: RadioStation) -> some View {
        HStack(spacing: 0) {
            Button(action: {
                handleStationTap(station)
            }) {
                HStack {
                    Text(station.name)
                        .foregroundColor(Color(hex: "8A2BE2"))

                    Spacer()

                    if selectedStation?.id == station.id {
                        Image(systemName: timerState == .running ? "pause.circle.fill" : "play.circle.fill")
                            .resizable()
                            .frame(width: 30, height: 30)
                            .foregroundColor(Color(hex: "9370DB"))
                    } else {
                        Image(systemName: "play.circle.fill")
                            .resizable()
                            .frame(width: 30, height: 30)
                            .foregroundColor(Color(hex: "E6E6FA"))
                    }
                }
                .padding()
                .frame(maxWidth: .infinity)
            }

            Button(action: {
                stationToDelete = station
                showingDeleteAlert = true
            }) {
                Image(systemName: "trash.circle.fill")
                    .resizable()
                    .frame(width: 24, height: 24)
                    .foregroundColor(.red)
                    .padding(.trailing)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color(hex: selectedStation?.id == station.id ? "9370DB" : "E6E6FA"), lineWidth: 2)
        )
    }

    // 处理电台点击
    private func handleStationTap(_ station: RadioStation) {
        if timerState == .running {
            if selectedStation?.id == station.id {
                RadioManager.shared.stopRadio()
                timerState = .initial
                selectedStation = nil
            } else {
                RadioManager.shared.stopRadio()
                RadioManager.shared.playRadio(url: station.url, stationName: station.name)
                selectedStation = station
            }
        } else {
            RadioManager.shared.playRadio(url: station.url, stationName: station.name)
            timerState = .running
            selectedStation = station
        }
    }
}

#Preview {
    RadioView()
}
