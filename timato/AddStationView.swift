import SwiftUI

struct AddStationView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var stationManager: RadioStationManager
    
    @State private var stationName = ""
    @State private var stationURL = ""
    @State private var isValidating = false
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                VStack(alignment: .leading, spacing: 20) {
                    Text(NSLocalizedString("add_station.title", comment: "Add station title"))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: "9370DB"))
                        .frame(maxWidth: .infinity, alignment: .center)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text(NSLocalizedString("add_station.name_label", comment: "Station name label"))
                            .font(.headline)
                            .foregroundColor(Color(hex: "8A2BE2"))

                        TextField(NSLocalizedString("add_station.name_placeholder", comment: "Station name placeholder"), text: $stationName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.none)
                            .disableAutocorrection(true)
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text(NSLocalizedString("add_station.url_label", comment: "Stream URL label"))
                            .font(.headline)
                            .foregroundColor(Color(hex: "8A2BE2"))

                        TextField(NSLocalizedString("add_station.url_placeholder", comment: "Stream URL placeholder"), text: $stationURL)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.none)
                            .disableAutocorrection(true)
                            .keyboardType(.URL)

                        Text(NSLocalizedString("add_station.url_description", comment: "URL description"))
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    
                    // 示例URL
                    VStack(alignment: .leading, spacing: 8) {
                        Text(NSLocalizedString("add_station.example_label", comment: "Example format label"))
                            .font(.caption)
                            .foregroundColor(.gray)

                        Text(NSLocalizedString("add_station.example_url", comment: "Example URL"))
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(4)
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // 按钮区域
                VStack(spacing: 15) {
                    Button(action: addStation) {
                        HStack {
                            if isValidating {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                Text(NSLocalizedString("add_station.validating", comment: "Validating text"))
                            } else {
                                Image(systemName: "plus.circle.fill")
                                Text(NSLocalizedString("add_station.add_button", comment: "Add station button"))
                            }
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(isFormValid ? Color(hex: "9370DB") : Color.gray)
                        )
                    }
                    .disabled(!isFormValid || isValidating)
                    
                    Button(action: {
                        dismiss()
                    }) {
                        Text(NSLocalizedString("add_station.cancel_button", comment: "Cancel button"))
                            .font(.headline)
                            .foregroundColor(Color(hex: "9370DB"))
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "9370DB"), lineWidth: 2)
                            )
                    }
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
            .navigationBarHidden(true)
        }
        .alert(alertTitle, isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private var isFormValid: Bool {
        !stationName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !stationURL.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    private func addStation() {
        let trimmedName = stationName.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedURL = stationURL.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedName.isEmpty && !trimmedURL.isEmpty else {
            showAlert(title: NSLocalizedString("add_station.error.input_error", comment: "Input error title"),
                     message: NSLocalizedString("add_station.error.input_message", comment: "Input error message"))
            return
        }

        // 基本URL格式验证
        guard URL(string: trimmedURL) != nil else {
            showAlert(title: NSLocalizedString("add_station.error.url_format", comment: "URL format error title"),
                     message: NSLocalizedString("add_station.error.url_format_message", comment: "URL format error message"))
            return
        }
        
        isValidating = true
        
        // 验证URL是否可播放
        stationManager.validateStationURL(trimmedURL) { isValid in
            DispatchQueue.main.async {
                self.isValidating = false
                
                if isValid {
                    // URL验证成功，尝试添加电台
                    if self.stationManager.addCustomStation(name: trimmedName, url: trimmedURL) {
                        self.dismiss()
                    } else {
                        self.showAlert(title: NSLocalizedString("add_station.error.add_failed", comment: "Add failed title"),
                                     message: NSLocalizedString("add_station.error.add_failed_message", comment: "Add failed message"))
                    }
                } else {
                    self.showAlert(title: NSLocalizedString("add_station.error.url_validation_failed", comment: "URL validation failed title"),
                                 message: NSLocalizedString("add_station.error.url_validation_message", comment: "URL validation failed message"))
                }
            }
        }
    }
    
    private func showAlert(title: String, message: String) {
        alertTitle = title
        alertMessage = message
        showingAlert = true
    }
}

#Preview {
    AddStationView(stationManager: RadioStationManager.shared)
}
