import SwiftUI

struct AddStationView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var stationManager: RadioStationManager
    
    @State private var stationName = ""
    @State private var stationURL = ""
    @State private var isValidating = false
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                VStack(alignment: .leading, spacing: 20) {
                    Text("添加自定义电台")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: "9370DB"))
                        .frame(maxWidth: .infinity, alignment: .center)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("电台名称")
                            .font(.headline)
                            .foregroundColor(Color(hex: "8A2BE2"))
                        
                        TextField("请输入电台名称", text: $stationName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.none)
                            .disableAutocorrection(true)
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("流媒体URL")
                            .font(.headline)
                            .foregroundColor(Color(hex: "8A2BE2"))
                        
                        TextField("请输入电台流媒体URL", text: $stationURL)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.none)
                            .disableAutocorrection(true)
                            .keyboardType(.URL)
                        
                        Text("支持 HTTP/HTTPS 协议的音频流")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    
                    // 示例URL
                    VStack(alignment: .leading, spacing: 8) {
                        Text("示例格式:")
                            .font(.caption)
                            .foregroundColor(.gray)
                        
                        Text("http://example.com/stream.mp3")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(4)
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // 按钮区域
                VStack(spacing: 15) {
                    Button(action: addStation) {
                        HStack {
                            if isValidating {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                Text("验证中...")
                            } else {
                                Image(systemName: "plus.circle.fill")
                                Text("添加电台")
                            }
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(isFormValid ? Color(hex: "9370DB") : Color.gray)
                        )
                    }
                    .disabled(!isFormValid || isValidating)
                    
                    Button(action: {
                        dismiss()
                    }) {
                        Text("取消")
                            .font(.headline)
                            .foregroundColor(Color(hex: "9370DB"))
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "9370DB"), lineWidth: 2)
                            )
                    }
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
            .navigationBarHidden(true)
        }
        .alert(alertTitle, isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private var isFormValid: Bool {
        !stationName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !stationURL.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    private func addStation() {
        let trimmedName = stationName.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedURL = stationURL.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedName.isEmpty && !trimmedURL.isEmpty else {
            showAlert(title: "输入错误", message: "请填写完整的电台信息")
            return
        }
        
        // 基本URL格式验证
        guard URL(string: trimmedURL) != nil else {
            showAlert(title: "URL格式错误", message: "请输入有效的URL地址")
            return
        }
        
        isValidating = true
        
        // 验证URL是否可播放
        stationManager.validateStationURL(trimmedURL) { isValid in
            DispatchQueue.main.async {
                self.isValidating = false
                
                if isValid {
                    // URL验证成功，尝试添加电台
                    if self.stationManager.addCustomStation(name: trimmedName, url: trimmedURL) {
                        self.dismiss()
                    } else {
                        self.showAlert(title: "添加失败", message: "电台名称或URL已存在")
                    }
                } else {
                    self.showAlert(title: "URL验证失败", message: "无法连接到该电台，请检查URL是否正确")
                }
            }
        }
    }
    
    private func showAlert(title: String, message: String) {
        alertTitle = title
        alertMessage = message
        showingAlert = true
    }
}

#Preview {
    AddStationView(stationManager: RadioStationManager.shared)
}
