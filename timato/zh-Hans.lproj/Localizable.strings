/* 
  Localizable.strings (Chinese Simplified)
  timato
  
  Created for Timato App
*/

// MARK: - Tab Bar Items
"tab.pomodoro" = "番茄钟";
"tab.radio" = "收音机";
"tab.tabata" = "Tabata";

// MARK: - Common Actions
"action.start" = "开始";
"action.pause" = "暂停";
"action.reset" = "重置";
"action.stop" = "停止";
"action.cancel" = "取消";
"action.add" = "添加";
"action.delete" = "删除";
"action.confirm" = "确认";

// MARK: - Alert Messages
"alert.timer_running.title" = "正在进行中";
"alert.pomodoro_running.message" = "番茄钟正在运行中，是否停止并切换？";
"alert.tabata_running.message" = "Tabata正在运行中，是否停止并切换？";
"alert.stop_and_switch" = "停止并切换";
"alert.delete_station.title" = "删除电台";
"alert.delete_station.message" = "确定要删除电台 \"%@\" 吗？";

// MARK: - Pomodoro Timer
"pomodoro.work_status" = "工作进行中，请关注当下哦";
"pomodoro.break_status" = "该休息了，站起来喝杯水吧～";
"pomodoro.work_time" = "工作时间";
"pomodoro.break_time" = "休息时间";
"pomodoro.minutes_format" = "%d 分钟";
"pomodoro.work_completed" = "工作时间结束！";
"pomodoro.break_completed" = "休息时间结束！";

// MARK: - Radio
"radio.playing" = "正在播放";
"radio.stopped" = "停止";
"radio.add_station" = "添加电台";
"radio.preset_stations" = "预设电台";
"radio.custom_stations" = "自定义电台";

// MARK: - Add Station
"add_station.title" = "添加自定义电台";
"add_station.name_label" = "电台名称";
"add_station.name_placeholder" = "请输入电台名称";
"add_station.url_label" = "流媒体URL";
"add_station.url_placeholder" = "请输入电台流媒体URL";
"add_station.url_description" = "支持 HTTP/HTTPS 协议的音频流";
"add_station.example_label" = "示例格式:";
"add_station.example_url" = "http://example.com/stream.mp3";
"add_station.validating" = "验证中...";
"add_station.add_button" = "添加电台";
"add_station.cancel_button" = "取消";

// MARK: - Add Station Errors
"add_station.error.input_error" = "输入错误";
"add_station.error.input_message" = "请填写完整的电台信息";
"add_station.error.url_format" = "URL格式错误";
"add_station.error.url_format_message" = "请输入有效的URL地址";
"add_station.error.add_failed" = "添加失败";
"add_station.error.add_failed_message" = "电台名称或URL已存在";
"add_station.error.url_validation_failed" = "URL验证失败";
"add_station.error.url_validation_message" = "无法连接到该电台，请检查URL是否正确";

// MARK: - Tabata Timer
"tabata.settings" = "Tabata 设置";
"tabata.exercise_time" = "运动时间";
"tabata.rest_time" = "休息时间";
"tabata.rounds" = "训练组数";
"tabata.seconds_format" = "%d 秒";
"tabata.rounds_format" = "%d 组";
"tabata.round_progress" = "第%d/%d组";
"tabata.exercise" = "运动";
"tabata.rest" = "休息";
"tabata.get_ready" = "准备开始";
"tabata.completed" = "训练完成！";

// MARK: - Time Formats
"time.minutes" = "分钟";
"time.seconds" = "秒";
"time.format.mm_ss" = "%02d:%02d";

// MARK: - Language Switching
"language.switch" = "切换语言";
"language.current" = "当前语言";
"language.english" = "英文";
"language.chinese" = "中文";
