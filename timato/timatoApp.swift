//
//  timatoApp.swift
//  timato
//
//  Created by <PERSON> on 15/3/25.
//

import SwiftUI
import UIKit

@main
struct timatoApp: App {
    init() {
        // 禁用屏幕自动锁定
        UIApplication.shared.isIdleTimerDisabled = true

        // 初始化语言管理器
        _ = LanguageManager.shared
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.languageManager, LanguageManager.shared)
        }
    }
}
