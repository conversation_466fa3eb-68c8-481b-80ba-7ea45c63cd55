import Foundation
import AVFoundation
import MediaPlayer

class RadioManager {
    static let shared = RadioManager()
    private var player: AVPlayer?
    private var audioSession: AVAudioSession

    init() {
        audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(.playback, mode: .default)
            try audioSession.setActive(true)
        } catch {
            print("Failed to set up audio session: \(error)")
        }
    }

    func stopRadio() {
        player?.pause()
        player = nil
        do {
            try audioSession.setActive(false)
        } catch {
            print("Failed to deactivate audio session: \(error)")
        }
        print("Radio stopped")
    }

    private func setupBackgroundPlayback() {
        let commandCenter = MPRemoteCommandCenter.shared()
        
        commandCenter.playCommand.addTarget { [weak self] _ in
            self?.player?.play()
            return .success
        }
        
        commandCenter.pauseCommand.addTarget { [weak self] _ in
            self?.player?.pause()
            return .success
        }
        
        setupNowPlaying()
    }
    
    private var currentStationName: String?
    
    private func setupNowPlaying() {
        var nowPlayingInfo = [String: Any]()
        nowPlayingInfo[MPMediaItemPropertyTitle] = currentStationName ?? "未知电台"
        nowPlayingInfo[MPMediaItemPropertyArtist] = "正在播放"
        
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
    
    func playRadio(url: String, stationName: String) {
        currentStationName = stationName
        do {
            try audioSession.setActive(true)
            guard let streamURL = URL(string: url) else {
                print("Invalid URL: \(url)")
                return
            }
            player = AVPlayer(url: streamURL)
            player?.play()
            setupBackgroundPlayback()
            print("Playing radio from URL: \(url)")
        } catch {
            print("Failed to activate audio session: \(error)")
        }
    }
}
