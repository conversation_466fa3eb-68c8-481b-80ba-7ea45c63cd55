import Foundation
import AVFoundation

// 扩展RadioStation结构，添加自定义电台标识
struct RadioStation: Identifiable, Codable {
    let id: UUID
    let name: String
    let url: String
    let isCustom: Bool

    init(name: String, url: String, isCustom: Bool = false) {
        self.id = UUID()
        self.name = name
        self.url = url
        self.isCustom = isCustom
    }
}

// 电台数据管理类
class RadioStationManager: ObservableObject {
    static let shared = RadioStationManager()
    
    @Published var allStations: [RadioStation] = []
    
    private let userDefaultsKey = "CustomRadioStations"
    
    // 预设电台
    private let presetStations = [
        RadioStation(name: "怀集音乐之声", url: "http://lhttp.qingting.fm/live/4804/64k.mp3"),
        RadioStation(name: "广东音乐之声", url: "https://lhttp-hw.qingting.fm/live/1260/64k.mp3"),
        RadioStation(name: "星岛中文粤语", url: "https://nap.casthost.net/proxy/florenc1/stream")
    ]
    
    private init() {
        loadStations()
    }
    
    // 加载所有电台（预设 + 自定义）
    private func loadStations() {
        var stations = presetStations
        
        // 加载自定义电台
        if let data = UserDefaults.standard.data(forKey: userDefaultsKey),
           let customStations = try? JSONDecoder().decode([RadioStation].self, from: data) {
            stations.append(contentsOf: customStations)
        }
        
        allStations = stations
    }
    
    // 保存自定义电台到UserDefaults
    private func saveCustomStations() {
        let customStations = allStations.filter { $0.isCustom }
        if let data = try? JSONEncoder().encode(customStations) {
            UserDefaults.standard.set(data, forKey: userDefaultsKey)
        }
    }
    
    // 添加自定义电台
    func addCustomStation(name: String, url: String) -> Bool {
        // 验证URL格式
        guard isValidURL(url) else {
            return false
        }
        
        // 检查是否已存在相同名称或URL的电台
        if allStations.contains(where: { $0.name == name || $0.url == url }) {
            return false
        }
        
        let newStation = RadioStation(name: name, url: url, isCustom: true)
        allStations.append(newStation)
        saveCustomStations()
        return true
    }
    
    // 删除自定义电台
    func deleteCustomStation(_ station: RadioStation) -> Bool {
        guard station.isCustom else {
            return false // 不能删除预设电台
        }
        
        if let index = allStations.firstIndex(where: { $0.id == station.id }) {
            allStations.remove(at: index)
            saveCustomStations()
            return true
        }
        return false
    }
    
    // URL验证
    private func isValidURL(_ urlString: String) -> Bool {
        guard let url = URL(string: urlString) else {
            return false
        }
        
        // 检查URL scheme
        guard let scheme = url.scheme?.lowercased(),
              ["http", "https"].contains(scheme) else {
            return false
        }
        
        // 检查是否有host
        guard url.host != nil else {
            return false
        }
        
        return true
    }
    
    // 验证电台URL是否可播放（异步）
    func validateStationURL(_ urlString: String, completion: @escaping (Bool) -> Void) {
        guard let url = URL(string: urlString) else {
            completion(false)
            return
        }
        
        let player = AVPlayer(url: url)
        
        // 监听播放器状态
        let observer = player.observe(\.status) { player, _ in
            DispatchQueue.main.async {
                switch player.status {
                case .readyToPlay:
                    completion(true)
                case .failed:
                    completion(false)
                default:
                    break
                }
            }
        }
        
        // 设置超时
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            observer.invalidate()
            if player.status == .unknown {
                completion(false)
            }
        }
    }
    
    // 获取预设电台
    var presetStationsList: [RadioStation] {
        return allStations.filter { !$0.isCustom }
    }
    
    // 获取自定义电台
    var customStationsList: [RadioStation] {
        return allStations.filter { $0.isCustom }
    }
}
