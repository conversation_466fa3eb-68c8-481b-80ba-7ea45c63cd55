import Foundation
import AudioToolbox

class SoundManager {
    static let shared = SoundManager()
    
    private init() {}
    
    // 完成提示音
    func playSound() {
        AudioServicesPlaySystemSound(1005) // 常规完成音效
    }

    // 滴答声
    func playTickSound() {
        AudioServicesPlaySystemSound(1057) // 响亮的滴答声
    }
    
    // Tabata运动结束音效
    func playExerciseEndSound() {
        AudioServicesPlaySystemSound(1023) // 清脆的运动结束音效
    }
    
    // Tabata全部完成音效
    func playTabataCompleteSound() {
        AudioServicesPlaySystemSound(1025) // 欢快的完成音效
    }
}
