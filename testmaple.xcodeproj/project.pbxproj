// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		18584F2F2D8596ED00EEC6D4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 18584F162D8596E900EEC6D4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 18584F1D2D8596E900EEC6D4;
			remoteInfo = testmaple;
		};
		18584F392D8596ED00EEC6D4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 18584F162D8596E900EEC6D4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 18584F1D2D8596E900EEC6D4;
			remoteInfo = testmaple;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		18584F1E2D8596E900EEC6D4 /* testmaple.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testmaple.app; sourceTree = BUILT_PRODUCTS_DIR; };
		18584F2E2D8596ED00EEC6D4 /* testmapleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = testmapleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		18584F382D8596ED00EEC6D4 /* testmapleUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = testmapleUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		18F15BE22D869E1100FE616E /* Exceptions for "testmaple" folder in "testmaple" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 18584F1D2D8596E900EEC6D4 /* testmaple */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		18584F202D8596E900EEC6D4 /* testmaple */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				18F15BE22D869E1100FE616E /* Exceptions for "testmaple" folder in "testmaple" target */,
			);
			path = testmaple;
			sourceTree = "<group>";
		};
		18584F312D8596ED00EEC6D4 /* testmapleTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = testmapleTests;
			sourceTree = "<group>";
		};
		18584F3B2D8596ED00EEC6D4 /* testmapleUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = testmapleUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		18584F1B2D8596E900EEC6D4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18584F2B2D8596ED00EEC6D4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18584F352D8596ED00EEC6D4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		18584F152D8596E900EEC6D4 = {
			isa = PBXGroup;
			children = (
				18584F202D8596E900EEC6D4 /* testmaple */,
				18584F312D8596ED00EEC6D4 /* testmapleTests */,
				18584F3B2D8596ED00EEC6D4 /* testmapleUITests */,
				18584F1F2D8596E900EEC6D4 /* Products */,
			);
			sourceTree = "<group>";
		};
		18584F1F2D8596E900EEC6D4 /* Products */ = {
			isa = PBXGroup;
			children = (
				18584F1E2D8596E900EEC6D4 /* testmaple.app */,
				18584F2E2D8596ED00EEC6D4 /* testmapleTests.xctest */,
				18584F382D8596ED00EEC6D4 /* testmapleUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		18584F1D2D8596E900EEC6D4 /* testmaple */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18584F422D8596ED00EEC6D4 /* Build configuration list for PBXNativeTarget "testmaple" */;
			buildPhases = (
				18584F1A2D8596E900EEC6D4 /* Sources */,
				18584F1B2D8596E900EEC6D4 /* Frameworks */,
				18584F1C2D8596E900EEC6D4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				18584F202D8596E900EEC6D4 /* testmaple */,
			);
			name = testmaple;
			packageProductDependencies = (
			);
			productName = testmaple;
			productReference = 18584F1E2D8596E900EEC6D4 /* testmaple.app */;
			productType = "com.apple.product-type.application";
		};
		18584F2D2D8596ED00EEC6D4 /* testmapleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18584F452D8596ED00EEC6D4 /* Build configuration list for PBXNativeTarget "testmapleTests" */;
			buildPhases = (
				18584F2A2D8596ED00EEC6D4 /* Sources */,
				18584F2B2D8596ED00EEC6D4 /* Frameworks */,
				18584F2C2D8596ED00EEC6D4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				18584F302D8596ED00EEC6D4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				18584F312D8596ED00EEC6D4 /* testmapleTests */,
			);
			name = testmapleTests;
			packageProductDependencies = (
			);
			productName = testmapleTests;
			productReference = 18584F2E2D8596ED00EEC6D4 /* testmapleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		18584F372D8596ED00EEC6D4 /* testmapleUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18584F482D8596ED00EEC6D4 /* Build configuration list for PBXNativeTarget "testmapleUITests" */;
			buildPhases = (
				18584F342D8596ED00EEC6D4 /* Sources */,
				18584F352D8596ED00EEC6D4 /* Frameworks */,
				18584F362D8596ED00EEC6D4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				18584F3A2D8596ED00EEC6D4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				18584F3B2D8596ED00EEC6D4 /* testmapleUITests */,
			);
			name = testmapleUITests;
			packageProductDependencies = (
			);
			productName = testmapleUITests;
			productReference = 18584F382D8596ED00EEC6D4 /* testmapleUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		18584F162D8596E900EEC6D4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					18584F1D2D8596E900EEC6D4 = {
						CreatedOnToolsVersion = 16.2;
					};
					18584F2D2D8596ED00EEC6D4 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 18584F1D2D8596E900EEC6D4;
					};
					18584F372D8596ED00EEC6D4 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 18584F1D2D8596E900EEC6D4;
					};
				};
			};
			buildConfigurationList = 18584F192D8596E900EEC6D4 /* Build configuration list for PBXProject "testmaple" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 18584F152D8596E900EEC6D4;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 18584F1F2D8596E900EEC6D4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				18584F1D2D8596E900EEC6D4 /* testmaple */,
				18584F2D2D8596ED00EEC6D4 /* testmapleTests */,
				18584F372D8596ED00EEC6D4 /* testmapleUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		18584F1C2D8596E900EEC6D4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18584F2C2D8596ED00EEC6D4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18584F362D8596ED00EEC6D4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		18584F1A2D8596E900EEC6D4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18584F2A2D8596ED00EEC6D4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18584F342D8596ED00EEC6D4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		18584F302D8596ED00EEC6D4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 18584F1D2D8596E900EEC6D4 /* testmaple */;
			targetProxy = 18584F2F2D8596ED00EEC6D4 /* PBXContainerItemProxy */;
		};
		18584F3A2D8596ED00EEC6D4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 18584F1D2D8596E900EEC6D4 /* testmaple */;
			targetProxy = 18584F392D8596ED00EEC6D4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		18584F402D8596ED00EEC6D4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		18584F412D8596ED00EEC6D4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		18584F432D8596ED00EEC6D4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"testmaple/Preview Content\"";
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = testmaple/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.testmaple.testmaple;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		18584F442D8596ED00EEC6D4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"testmaple/Preview Content\"";
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = testmaple/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.testmaple.testmaple;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		18584F462D8596ED00EEC6D4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.testmaple.testmapleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/testmaple.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/testmaple";
			};
			name = Debug;
		};
		18584F472D8596ED00EEC6D4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.testmaple.testmapleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/testmaple.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/testmaple";
			};
			name = Release;
		};
		18584F492D8596ED00EEC6D4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.testmaple.testmapleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = testmaple;
			};
			name = Debug;
		};
		18584F4A2D8596ED00EEC6D4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.testmaple.testmapleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = testmaple;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		18584F192D8596E900EEC6D4 /* Build configuration list for PBXProject "testmaple" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18584F402D8596ED00EEC6D4 /* Debug */,
				18584F412D8596ED00EEC6D4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18584F422D8596ED00EEC6D4 /* Build configuration list for PBXNativeTarget "testmaple" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18584F432D8596ED00EEC6D4 /* Debug */,
				18584F442D8596ED00EEC6D4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18584F452D8596ED00EEC6D4 /* Build configuration list for PBXNativeTarget "testmapleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18584F462D8596ED00EEC6D4 /* Debug */,
				18584F472D8596ED00EEC6D4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18584F482D8596ED00EEC6D4 /* Build configuration list for PBXNativeTarget "testmapleUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18584F492D8596ED00EEC6D4 /* Debug */,
				18584F4A2D8596ED00EEC6D4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 18584F162D8596E900EEC6D4 /* Project object */;
}
