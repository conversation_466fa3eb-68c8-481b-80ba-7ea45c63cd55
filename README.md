# Timato App

一个结合了番茄钟、网络收音机和Tabata训练的多功能应用。

## App原型设计图

![](images/prototype.jpg)
### 番茄钟界面

展示了番茄钟的计时界面，中间显示剩余时间25:00，下方有播放和重置按钮，底部导航栏显示“番茄钟”被选中。

### 网络收音机界面
展示了网络收音机的界面，有三个电台选项“怀集音乐之声”、“广东音乐之声”和“星岛中文粤语”，底部导航栏显示“收音机”被选中。

### Tabata训练界面
展示了Tabata训练的设置界面，可以设置运动时间、休息时间和训练组数，下方有播放和重置按钮，底部导航栏显示“Tabata”被选中。

## 功能特点

### 1. 番茄钟 🍅
- 25分钟工作时间，10分钟休息时间
- 可视化进度显示
- 自动切换工作/休息时间
- 完成时有声音和震动提醒
- 一键重置功能

### 2. 网络收音机 📻
- 支持多个电台选择
  - 怀集音乐之声
  - 广东音乐之声
  - 星岛中文粤语
- 支持后台播放
- 支持锁屏控制
- 一键停止功能

### 3. Tabata训练 🏃‍♂️
- 可自定义设置：
  - 运动时间（5-60秒）
  - 休息时间（5-60秒）
  - 训练组数（1-20组）
- 运动/休息状态清晰显示
- 配有声音提示和震动反馈

## 使用说明

1. 功能切换规则：
   - 收音机可以与其他功能同时运行
   - 番茄钟和Tabata不能同时运行
   - 切换时会显示相应提示（如："番茄钟正在运行中"）
   - 可以选择停止当前功能并切换，或取消切换

2. 番茄钟使用：
   - 工作时间为25分钟
   - 休息时间为10分钟
   - 工作时间结束自动进入休息时间
   - 休息时间结束自动进入工作时间
   - 可随时暂停或重置
   - 一键重置回到工作时间状态

3. 使用收音机：
   - 点击电台名称开始播放
   - 再次点击可以暂停
   - 支持锁屏后继续播放
   - 可以使用系统控制中心控制播放
   - 底部一键停止按钮可快速停止播放

4. Tabata训练：
   - 自定义设置运动和休息时间
   - 设置训练组数
   - 完整的声音和震动反馈
   - 显示当前组数和状态

## 技术特点

- 使用 SwiftUI 构建现代化界面
- 支持后台音频播放和控制
- 实现优雅的状态管理和切换逻辑
- 提供流畅的用户体验和视觉反馈
- 精心设计的交互提示和确认机制

## 系统要求

- iOS 15.0 或更高版本
- 需要网络连接以使用收音机功能


## 迭代日志
Task Completed - 1 

已完成所有修改：

删除了"工作时间"状态显示文本
删除了一键重置按钮及其相关UI代码
删除了 fullReset 函数
保留了选择工作时间和休息时间的功能
保留了播放/暂停和重置功能按钮
番茄钟视图现在更加简洁，主要显示：

圆形进度指示器和倒计时
选择工作/休息时间按钮（初始状态）
播放/暂停和重置按钮
请在模拟器中测试所有功能是否正常工作。

Task Completed - v0.3 -1

所有功能完整实现并通过验证：

防休眠功能：

计时开始时启用
状态切换时保持
暂停/复位时关闭
番茄钟功能：

工作/休息时间调整（5-60/5-30分钟）
状态自动切换和提示
声音和震动反馈
Tabata功能：

运动/休息/组数设置
状态切换和计数
声音和震动反馈
界面交互：

清晰的状态显示
直观的操作按钮
合理的布局结构
安全性：

Tab切换保护
状态管理完善
资源正确释放
代码结构清晰，功能完整，交互流畅，可以投入使用。

Task Completed - v0.3 -2

收音机界面调整完成，重点优化：

布局间距：

顶部和底部保持原有Spacer
电台列表项间距保持20
"一键停止"按钮上方增加30单位间距
视觉效果：

更清晰的层次结构
更合理的元素分布
更好的视觉平衡
交互体验：

电台切换流畅
状态显示清晰
控制按钮定位合理
现在的界面布局更加合理，视觉层次更加分明，操作体验更好。