//
//  LanguageManagerTests.swift
//  timatoTests
//
//  Created for Timato App
//

import Testing
import Foundation
@testable import timato

struct LanguageManagerTests {
    
    @Test func testLanguageManagerSingleton() async throws {
        let manager1 = LanguageManager.shared
        let manager2 = LanguageManager.shared
        
        #expect(manager1 === manager2)
    }
    
    @Test func testSupportedLanguages() async throws {
        let languages = SupportedLanguage.allCases
        
        #expect(languages.count == 2)
        #expect(languages.contains(.english))
        #expect(languages.contains(.chinese))
    }
    
    @Test func testLanguageDisplayNames() async throws {
        #expect(SupportedLanguage.english.displayName == "English")
        #expect(SupportedLanguage.chinese.displayName == "中文")
    }
    
    @Test func testLanguageFlags() async throws {
        #expect(SupportedLanguage.english.flag == "🇺🇸")
        #expect(SupportedLanguage.chinese.flag == "🇨🇳")
    }
    
    @Test func testLanguageToggle() async throws {
        let manager = LanguageManager.shared
        let initialLanguage = manager.currentLanguage
        
        manager.toggleLanguage()
        let toggledLanguage = manager.currentLanguage
        
        #expect(initialLanguage != toggledLanguage)
        
        manager.toggleLanguage()
        let backToInitial = manager.currentLanguage
        
        #expect(backToInitial == initialLanguage)
    }
    
    @Test func testSetSpecificLanguage() async throws {
        let manager = LanguageManager.shared
        
        manager.setLanguage(.english)
        #expect(manager.currentLanguage == .english)
        
        manager.setLanguage(.chinese)
        #expect(manager.currentLanguage == .chinese)
    }
    
    @Test func testLanguagePreferencePersistence() async throws {
        let manager = LanguageManager.shared
        let userDefaults = UserDefaults.standard
        
        // 设置为英文
        manager.setLanguage(.english)
        let savedEnglish = userDefaults.string(forKey: "AppLanguage")
        #expect(savedEnglish == "en")
        
        // 设置为中文
        manager.setLanguage(.chinese)
        let savedChinese = userDefaults.string(forKey: "AppLanguage")
        #expect(savedChinese == "zh-Hans")
    }
    
    @Test func testLocalizedStringFunction() async throws {
        let manager = LanguageManager.shared
        
        // 测试本地化字符串函数
        let localizedString = manager.localizedString(for: "tab.pomodoro", comment: "Pomodoro tab")
        
        #expect(!localizedString.isEmpty)
        #expect(localizedString != "tab.pomodoro") // 应该返回本地化后的字符串，而不是键名
    }
}
