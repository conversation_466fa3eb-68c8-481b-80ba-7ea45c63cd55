//
//  LocalizationTests.swift
//  timatoTests
//
//  Created for Timato App
//

import Testing
import Foundation
@testable import timato

struct LocalizationTests {
    
    @Test func testChineseLocalization() async throws {
        // 测试中文本地化
        let bundle = Bundle.main
        
        // 测试基本的本地化字符串
        let pomodoroTab = NSLocalizedString("tab.pomodoro", bundle: bundle, comment: "Pomodoro tab title")
        let radioTab = NSLocalizedString("tab.radio", bundle: bundle, comment: "Radio tab title")
        let tabataTab = NSLocalizedString("tab.tabata", bundle: bundle, comment: "Tabata tab title")
        
        // 验证字符串不为空且不等于键名（说明本地化生效）
        #expect(!pomodoroTab.isEmpty)
        #expect(!radioTab.isEmpty)
        #expect(!tabataTab.isEmpty)
        
        // 验证返回的不是键名本身（如果本地化失败，会返回键名）
        #expect(pomodoroTab != "tab.pomodoro")
        #expect(radioTab != "tab.radio")
        #expect(tabataTab != "tab.tabata")
    }
    
    @Test func testActionStrings() async throws {
        let bundle = Bundle.main
        
        let startAction = NSLocalizedString("action.start", bundle: bundle, comment: "Start button")
        let pauseAction = NSLocalizedString("action.pause", bundle: bundle, comment: "Pause button")
        let resetAction = NSLocalizedString("action.reset", bundle: bundle, comment: "Reset button")
        
        #expect(!startAction.isEmpty)
        #expect(!pauseAction.isEmpty)
        #expect(!resetAction.isEmpty)
        
        #expect(startAction != "action.start")
        #expect(pauseAction != "action.pause")
        #expect(resetAction != "action.reset")
    }
    
    @Test func testPomodoroStrings() async throws {
        let bundle = Bundle.main
        
        let workStatus = NSLocalizedString("pomodoro.work_status", bundle: bundle, comment: "Work status message")
        let breakStatus = NSLocalizedString("pomodoro.break_status", bundle: bundle, comment: "Break status message")
        
        #expect(!workStatus.isEmpty)
        #expect(!breakStatus.isEmpty)
        
        #expect(workStatus != "pomodoro.work_status")
        #expect(breakStatus != "pomodoro.break_status")
    }
    
    @Test func testRadioStrings() async throws {
        let bundle = Bundle.main
        
        let addStation = NSLocalizedString("radio.add_station", bundle: bundle, comment: "Add station button")
        let presetStations = NSLocalizedString("radio.preset_stations", bundle: bundle, comment: "Preset stations title")
        let customStations = NSLocalizedString("radio.custom_stations", bundle: bundle, comment: "Custom stations title")
        
        #expect(!addStation.isEmpty)
        #expect(!presetStations.isEmpty)
        #expect(!customStations.isEmpty)
        
        #expect(addStation != "radio.add_station")
        #expect(presetStations != "radio.preset_stations")
        #expect(customStations != "radio.custom_stations")
    }
    
    @Test func testTabataStrings() async throws {
        let bundle = Bundle.main
        
        let settings = NSLocalizedString("tabata.settings", bundle: bundle, comment: "Tabata settings title")
        let exercise = NSLocalizedString("tabata.exercise", bundle: bundle, comment: "Exercise status")
        let rest = NSLocalizedString("tabata.rest", bundle: bundle, comment: "Rest status")
        
        #expect(!settings.isEmpty)
        #expect(!exercise.isEmpty)
        #expect(!rest.isEmpty)
        
        #expect(settings != "tabata.settings")
        #expect(exercise != "tabata.exercise")
        #expect(rest != "tabata.rest")
    }
}
